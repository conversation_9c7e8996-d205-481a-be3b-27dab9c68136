package com.app.gamehub.service;

import com.app.gamehub.dto.WarRequest;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.WarApplicationRepository;
import com.app.gamehub.repository.WarArrangementRepository;
import com.app.gamehub.util.UserContext;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class WarService {

  private final WarApplicationRepository warApplicationRepository;
  private final GameAccountRepository gameAccountRepository;
  private final AllianceRepository allianceRepository;
  private final WarArrangementRepository warArrangementRepository;

  @Transactional
  public WarApplication applyForWar(WarRequest request) {
    Long userId = UserContext.getUserId();
    Long accountId = request.getAccountId();

    // 验证账号是否存在且属于当前用户
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    if (!account.getUserId().equals(userId)) {
      throw new BusinessException("只能为自己的账号申请参加战事");
    }

    // 验证账号是否已加入联盟
    if (account.getAllianceId() == null) {
      throw new BusinessException("必须先加入联盟才能申请参加战事");
    }

    // 验证战事类型（只能申请官渡一或官渡二）
    if (request.getWarType() != WarType.GUANDU_ONE && request.getWarType() != WarType.GUANDU_TWO) {
      throw new BusinessException("只能申请参加官渡一或官渡二战事");
    }

    // 检查是否已有官渡战事的待处理申请
    List<WarType> guanduWars = Arrays.asList(WarType.GUANDU_ONE, WarType.GUANDU_TWO);
    if (warApplicationRepository.existsByAccountIdAndWarTypeIn(accountId, guanduWars)) {
      throw new BusinessException("只能参与一场官渡战事");
    }

    // 创建申请
    WarApplication application = new WarApplication();
    application.setAccountId(accountId);
    application.setAllianceId(account.getAllianceId());
    application.setWarType(request.getWarType());
    application.setStatus(WarApplication.ApplicationStatus.PENDING);

    return warApplicationRepository.save(application);
  }

  @Transactional
  public WarApplication processWarApplication(Long applicationId, boolean approved) {
    Long userId = UserContext.getUserId();
    WarApplication application =
        warApplicationRepository
            .findById(applicationId)
            .orElseThrow(() -> new BusinessException("申请不存在"));

    // 验证是否为盟主
    Alliance alliance =
        allianceRepository
            .findById(application.getAllianceId())
            .orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以处理战事申请");
    }

    // 验证申请状态
    if (application.getStatus() != WarApplication.ApplicationStatus.PENDING) {
      throw new BusinessException("申请已被处理");
    }

    if (approved) {
      if (WarType.isGuanDu(application.getWarType())) {
        if (warArrangementRepository.existsByAccountIdAndWarTypeIn(
            application.getAccountId(), WarType.allGuanDu())) {
          throw new BusinessException("一个账号只能参与一场官渡战事");
        }
      }
      application.setStatus(WarApplication.ApplicationStatus.APPROVED);
      WarArrangement arrangement = new WarArrangement();
      arrangement.setAccountId(application.getAccountId());
      arrangement.setAllianceId(alliance.getId());
      arrangement.setWarType(application.getWarType());
      warArrangementRepository.save(arrangement);
    } else {
      application.setStatus(WarApplication.ApplicationStatus.REJECTED);
    }

    application.setProcessedBy(userId);
    return warApplicationRepository.save(application);
  }

  public List<WarApplication> getPendingWarApplications(Long allianceId, WarType warType) {
    Long userId = UserContext.getUserId();
    // 验证是否为盟主
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以查看战事申请列表");
    }

    return warApplicationRepository.findByAllianceIdAndWarTypeAndStatusOrderByCreatedAtAsc(
        allianceId, warType, WarApplication.ApplicationStatus.PENDING);
  }

  public List<WarApplication> getAccountWarApplications(Long accountId) {
    return warApplicationRepository.findByAccountIdOrderByCreatedAtDesc(accountId);
  }

  public WarArrangement moveGuanDuWar(Long accountId) {
    gameAccountRepository.findById(accountId).orElseThrow(() -> new BusinessException("账号不存在"));
    List<WarArrangement> arrangements =
        warArrangementRepository.findByAccountIdAndWarTypeIn(
            accountId, List.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO));
    if (arrangements.size() != 1) {
      throw new BusinessException("账号不能同时参加官渡一和官渡二");
    }
    WarArrangement warArrangement = arrangements.getFirst();
    if (warArrangement.getWarType().equals(WarType.GUANDU_ONE)) {
      warArrangement.setWarType(WarType.GUANDU_TWO);
    } else {
      warArrangement.setWarType(WarType.GUANDU_ONE);
    }
    warArrangement.setWarGroupId(null);
    warArrangementRepository.save(warArrangement);
    return warArrangement;
  }

  public WarArrangement addToWar(Long accountId, WarType warType) {
    GameAccount account =
        gameAccountRepository.findById(accountId).orElseThrow(() -> new BusinessException("账号不存在"));
    Alliance alliance =
        allianceRepository
            .findById(account.getAllianceId())
            .orElseThrow(() -> new BusinessException("账号所在的联盟不存在"));
    if (!UserContext.getUserId().equals(alliance.getLeaderId())) {
      throw new BusinessException("只有盟主才能添加成员到战事中");
    }
    if (warType == WarType.GUANDU_ONE || warType == WarType.GUANDU_TWO) {
      List<WarArrangement> arrangements =
          warArrangementRepository.findByAccountIdAndWarTypeIn(
              accountId, List.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO));
      if (arrangements.size() == 1) {
        WarArrangement arrangement = arrangements.getFirst();
        if (arrangement.getWarType().equals(warType)) {
          throw new BusinessException("当前成员已在当前战事中");
        } else {
          throw new BusinessException("当前成员不能同时参加官渡一和官渡二");
        }
      }
    }
    WarArrangement arrangement = new WarArrangement();
    arrangement.setAccountId(accountId);
    arrangement.setAllianceId(alliance.getId());
    arrangement.setWarType(warType);
    warArrangementRepository.save(arrangement);
    return arrangement;
  }

  @Transactional
  public void removeFromWar(Long accountId, WarType warType) {
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("当前账号不存在"));
    Alliance alliance =
        allianceRepository
            .findById(account.getAllianceId())
            .orElseThrow(() -> new BusinessException("账号所在联盟不存在"));
    if (!UserContext.getUserId().equals(alliance.getLeaderId())) {
      throw new BusinessException("只有联盟盟主才能移除战事中的成员");
    }
    warArrangementRepository.deleteByAccountIdAndWarType(accountId, warType);
  }

  @Transactional
  public void cancelApplyForWar(@Valid WarRequest warRequest) {
    GameAccount account =
        gameAccountRepository
            .findById(warRequest.getAccountId())
            .orElseThrow(() -> new BusinessException("账号不存在"));
    if (!account.getUserId().equals(UserContext.getUserId())) {
      throw new BusinessException("不能取消他人的战事申请");
    }
    warApplicationRepository.deleteByAccountIdAndWarType(
        warRequest.getAccountId(), warRequest.getWarType());
  }
}
