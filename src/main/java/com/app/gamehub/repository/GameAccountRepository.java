package com.app.gamehub.repository;

import com.app.gamehub.entity.GameAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GameAccountRepository extends JpaRepository<GameAccount, Long> {
    
    List<GameAccount> findByUserIdOrderByServerIdDesc(Long userId);
    
    List<GameAccount> findByAllianceIdOrderByPowerValueDesc(Long allianceId);
    
    List<GameAccount> findByUserIdAndServerId(Long userId, Integer serverId);
    
    long countByUserIdAndServerId(Long userId, Integer serverId);
    
    long countByAllianceId(Long allianceId);
    
    boolean existsByUserIdAndServerIdAndAllianceIdIsNotNull(Long userId, Integer serverId);
}
